/**
 * Simple Database Test - No Jest, No Mocks
 * This test verifies that user creation works correctly
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');
require('dotenv').config();

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
  log: ['error', 'warn'],
});

async function testUserCreation() {
  console.log('🧪 Testing User Creation...');
  
  try {
    // Test 1: Basic connection
    await prisma.$connect();
    console.log('✅ Database connected');

    // Test 2: Create a test user (like the failing test)
    const hashedPassword = await bcrypt.hash('testpassword123', 10);
    const testEmail = `testuser-${Date.now()}@example.com`;
    
    const testUser1 = await prisma.user.upsert({
      where: { email: testEmail },
      update: {},
      create: {
        email: testEmail,
        password: hashedPassword,
        name: 'Test User 1',
        emailVerified: new Date(),
      },
    });

    console.log('✅ User created successfully:', {
      id: testUser1.id,
      email: testUser1.email,
      name: testUser1.name,
      hasId: !!testUser1.id,
      idType: typeof testUser1.id
    });

    // Test 3: Verify the user exists
    const foundUser = await prisma.user.findUnique({
      where: { email: testEmail }
    });

    if (!foundUser) {
      throw new Error('User not found after creation');
    }

    console.log('✅ User verification successful');

    // Test 4: Test the exact scenario from the failing test
    if (!testUser1 || !testUser1.id) {
      throw new Error(`testUser1 creation failed - got: ${JSON.stringify(testUser1)}`);
    }

    console.log('✅ User creation validation passed');

    // Cleanup
    await prisma.user.delete({
      where: { id: testUser1.id }
    });

    console.log('✅ Cleanup completed');

    console.log('\n🎉 All user creation tests passed!');
    console.log('The database connection and user creation is working correctly.');
    console.log('The issue is likely with the Jest test configuration, not the database.');

  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

testUserCreation();
