#!/usr/bin/env python3
"""
AI-Powered Web Testing System
Free alternative to operative.sh with local LLM integration
"""

from playwright.sync_api import sync_playwright
import json
import time
import os
from dataclasses import dataclass
from typing import List, Dict, Optional
import subprocess

@dataclass
class TestResult:
    test_name: str
    status: str
    details: str
    screenshot_path: str
    timestamp: str

class AIWebTester:
    def __init__(self, headless: bool = False):
        self.playwright = sync_playwright().start()
        self.browser = self.playwright.chromium.launch(headless=headless)
        self.results: List[TestResult] = []
        self.screenshots_dir = "test-screenshots"
        os.makedirs(self.screenshots_dir, exist_ok=True)
    
    def run_comprehensive_test(self, url: str, task: str = "comprehensive testing"):
        """Main testing entry point"""
        print(f"🚀 Starting AI Web Testing for: {url}")
        print(f"📋 Task: {task}")
        
        page = self.browser.new_page()
        
        # Enable console logging
        page.on("console", lambda msg: print(f"🖥️ Console [{msg.type}]: {msg.text}"))
        
        try:
            page.goto(url, wait_until="networkidle")
            
            # Run all test suites
            tests = [
                self.test_page_load,
                self.test_accessibility,
                self.test_forms,
                self.test_navigation,
                self.test_responsive_design,
                self.test_performance,
                self.test_security_basics,
                self.test_user_flows
            ]
            
            for test in tests:
                try:
                    print(f"🧪 Running {test.__name__}...")
                    result = test(page)
                    self.results.append(result)
                    print(f"   {'✅' if result.status == 'PASSED' else '❌'} {result.status}: {result.details}")
                except Exception as e:
                    error_result = TestResult(
                        test_name=test.__name__,
                        status="ERROR",
                        details=str(e),
                        screenshot_path=self.take_screenshot(page, f"error_{test.__name__}"),
                        timestamp=self.get_timestamp()
                    )
                    self.results.append(error_result)
                    print(f"   ❌ ERROR: {str(e)}")
        
        finally:
            page.close()
        
        return self.generate_report()
    
    def test_page_load(self, page):
        """Test basic page loading"""
        title = page.title()
        url = page.url
        
        issues = []
        if not title or title == "":
            issues.append("Missing page title")
        if "error" in title.lower() or "404" in title:
            issues.append(f"Error page detected: {title}")
        
        status = "FAILED" if issues else "PASSED"
        details = f"Title: '{title}'" + (f" | Issues: {'; '.join(issues)}" if issues else "")
        
        return TestResult("page_load", status, details, "", self.get_timestamp())
    
    def test_accessibility(self, page):
        """Test accessibility compliance"""
        issues = []
        
        # Check for alt tags on images
        images_without_alt = page.query_selector_all('img:not([alt])')
        if images_without_alt:
            issues.append(f"{len(images_without_alt)} images missing alt text")
        
        # Check for form labels
        inputs = page.query_selector_all('input[type="text"], input[type="email"], input[type="password"], textarea')
        unlabeled_inputs = []
        
        for inp in inputs:
            input_id = inp.get_attribute("id")
            has_label = False
            
            if input_id:
                label = page.query_selector(f'label[for="{input_id}"]')
                if label:
                    has_label = True
            
            if not has_label and not inp.get_attribute("aria-label"):
                unlabeled_inputs.append(inp)
        
        if unlabeled_inputs:
            issues.append(f"{len(unlabeled_inputs)} inputs without proper labels")
        
        # Check for heading structure
        headings = page.query_selector_all('h1, h2, h3, h4, h5, h6')
        if not headings:
            issues.append("No heading structure found")
        
        status = "FAILED" if issues else "PASSED"
        details = "; ".join(issues) if issues else "All accessibility checks passed"
        
        return TestResult("accessibility", status, details, "", self.get_timestamp())
    
    def test_forms(self, page):
        """Test form functionality"""
        forms = page.query_selector_all('form')
        
        if not forms:
            return TestResult("forms", "SKIPPED", "No forms found", "", self.get_timestamp())
        
        issues = []
        tested_forms = 0
        
        for i, form in enumerate(forms):
            tested_forms += 1
            
            # Test form inputs
            inputs = form.query_selector_all('input, textarea, select')
            
            for inp in inputs:
                input_type = inp.get_attribute("type") or "text"
                
                # Test different input scenarios
                if input_type == "email":
                    # Test invalid email
                    inp.fill("invalid-email")
                    # Check if validation appears (wait briefly)
                    page.wait_for_timeout(500)
                    
                elif input_type == "password":
                    # Test weak password
                    inp.fill("123")
                    page.wait_for_timeout(500)
                    
                elif input_type in ["text", "textarea"]:
                    # Test XSS attempt
                    inp.fill("<script>alert('test')</script>")
                    page.wait_for_timeout(500)
        
        status = "PASSED"
        details = f"Tested {tested_forms} forms with various input scenarios"
        
        return TestResult("forms", status, details, "", self.get_timestamp())
    
    def test_navigation(self, page):
        """Test navigation functionality"""
        issues = []
        
        # Test main navigation links
        nav_links = page.query_selector_all('nav a, header a')
        working_links = 0
        broken_links = 0
        
        for link in nav_links[:5]:  # Test first 5 links to avoid too many requests
            href = link.get_attribute("href")
            if href and not href.startswith("#") and not href.startswith("mailto:"):
                try:
                    # Test if link is clickable
                    if link.is_visible():
                        working_links += 1
                    else:
                        broken_links += 1
                except:
                    broken_links += 1
        
        if broken_links > 0:
            issues.append(f"{broken_links} navigation links not accessible")
        
        status = "FAILED" if issues else "PASSED"
        details = f"Tested {working_links + broken_links} navigation links" + (f" | Issues: {'; '.join(issues)}" if issues else "")
        
        return TestResult("navigation", status, details, "", self.get_timestamp())
    
    def test_responsive_design(self, page):
        """Test responsive design"""
        issues = []
        
        # Test different viewport sizes
        viewports = [
            {"width": 375, "height": 667, "name": "Mobile"},
            {"width": 768, "height": 1024, "name": "Tablet"},
            {"width": 1920, "height": 1080, "name": "Desktop"}
        ]
        
        for viewport in viewports:
            page.set_viewport_size({"width": viewport["width"], "height": viewport["height"]})
            page.wait_for_timeout(1000)
            
            # Check for horizontal scrollbar
            has_horizontal_scroll = page.evaluate("document.body.scrollWidth > window.innerWidth")
            if has_horizontal_scroll:
                issues.append(f"Horizontal scroll on {viewport['name']}")
            
            # Take screenshot for each viewport
            screenshot_path = self.take_screenshot(page, f"responsive_{viewport['name'].lower()}")
        
        status = "FAILED" if issues else "PASSED"
        details = f"Tested {len(viewports)} viewport sizes" + (f" | Issues: {'; '.join(issues)}" if issues else "")
        
        return TestResult("responsive_design", status, details, screenshot_path, self.get_timestamp())
    
    def test_performance(self, page):
        """Test basic performance metrics"""
        issues = []
        
        # Measure load time
        load_metrics = page.evaluate("""
            () => {
                const timing = performance.timing;
                return {
                    loadTime: timing.loadEventEnd - timing.navigationStart,
                    domReady: timing.domContentLoadedEventEnd - timing.navigationStart,
                    firstPaint: performance.getEntriesByType('paint')[0]?.startTime || 0
                };
            }
        """)
        
        if load_metrics["loadTime"] > 5000:
            issues.append(f"Slow load time: {load_metrics['loadTime']}ms")
        
        # Check for large images
        large_images = page.evaluate("""
            () => Array.from(document.images)
                .filter(img => img.naturalWidth > 2000 || img.naturalHeight > 2000)
                .length
        """)
        
        if large_images > 0:
            issues.append(f"{large_images} oversized images detected")
        
        status = "FAILED" if issues else "PASSED"
        details = f"Load: {load_metrics['loadTime']}ms, DOM: {load_metrics['domReady']}ms" + (f" | Issues: {'; '.join(issues)}" if issues else "")
        
        return TestResult("performance", status, details, "", self.get_timestamp())
    
    def test_security_basics(self, page):
        """Test basic security measures"""
        issues = []
        
        # Check for HTTPS
        if not page.url.startswith("https://") and not page.url.startswith("http://localhost"):
            issues.append("Not using HTTPS")
        
        # Check for mixed content
        mixed_content = page.evaluate("""
            () => {
                const resources = performance.getEntriesByType('resource');
                return resources.filter(r => 
                    location.protocol === 'https:' && r.name.startsWith('http:')
                ).length;
            }
        """)
        
        if mixed_content > 0:
            issues.append(f"{mixed_content} mixed content resources")
        
        status = "FAILED" if issues else "PASSED"
        details = "Basic security checks passed" if not issues else "; ".join(issues)
        
        return TestResult("security_basics", status, details, "", self.get_timestamp())
    
    def test_user_flows(self, page):
        """Test critical user flows"""
        issues = []
        flows_tested = 0
        
        # Test signup flow
        signup_button = page.query_selector('text=Sign Up')
        if signup_button and signup_button.is_visible():
            flows_tested += 1
            try:
                signup_button.click()
                page.wait_for_timeout(2000)
                # Check if signup form appears
                email_input = page.query_selector('input[type="email"]')
                if not email_input:
                    issues.append("Signup flow broken - no email input")
            except Exception as e:
                issues.append(f"Signup flow error: {str(e)}")
        
        # Test login flow
        login_button = page.query_selector('text=Log In')
        if login_button and login_button.is_visible():
            flows_tested += 1
            try:
                login_button.click()
                page.wait_for_timeout(2000)
            except Exception as e:
                issues.append(f"Login flow error: {str(e)}")
        
        status = "FAILED" if issues else "PASSED"
        details = f"Tested {flows_tested} user flows" + (f" | Issues: {'; '.join(issues)}" if issues else "")
        
        return TestResult("user_flows", status, details, "", self.get_timestamp())
    
    def take_screenshot(self, page, name: str = "screenshot"):
        """Take screenshot and return path"""
        timestamp = int(time.time())
        path = os.path.join(self.screenshots_dir, f"{name}_{timestamp}.png")
        page.screenshot(path=path, full_page=True)
        return path
    
    def get_timestamp(self):
        """Get current timestamp"""
        return time.strftime("%Y-%m-%d %H:%M:%S")
    
    def generate_report(self):
        """Generate comprehensive test report"""
        passed = len([r for r in self.results if r.status == "PASSED"])
        failed = len([r for r in self.results if r.status == "FAILED"])
        errors = len([r for r in self.results if r.status == "ERROR"])
        skipped = len([r for r in self.results if r.status == "SKIPPED"])
        
        success_rate = (passed / len(self.results) * 100) if self.results else 0
        
        report = f"""
🤖 AI Web Testing Report
========================
📊 Summary:
   Total Tests: {len(self.results)}
   ✅ Passed: {passed}
   ❌ Failed: {failed}
   🚨 Errors: {errors}
   ⏭️ Skipped: {skipped}
   📈 Success Rate: {success_rate:.1f}%

📋 Detailed Results:
"""
        
        for result in self.results:
            status_emoji = {"PASSED": "✅", "FAILED": "❌", "ERROR": "🚨", "SKIPPED": "⏭️"}
            report += f"   {status_emoji.get(result.status, '❓')} {result.test_name}: {result.status}\n"
            if result.details:
                report += f"      Details: {result.details}\n"
            if result.screenshot_path:
                report += f"      Screenshot: {result.screenshot_path}\n"
            report += "\n"
        
        # Save report to file
        report_path = f"test-report-{int(time.time())}.txt"
        with open(report_path, 'w') as f:
            f.write(report)
        
        print(f"📄 Full report saved to: {report_path}")
        return report
    
    def cleanup(self):
        """Clean up resources"""
        self.browser.close()
        self.playwright.stop()

# CLI Usage
if __name__ == "__main__":
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: python ai_web_tester.py <url> [task_description]")
        sys.exit(1)
    
    url = sys.argv[1]
    task = sys.argv[2] if len(sys.argv) > 2 else "comprehensive testing"
    
    tester = AIWebTester(headless=False)
    try:
        report = tester.run_comprehensive_test(url, task)
        print(report)
    finally:
        tester.cleanup()
