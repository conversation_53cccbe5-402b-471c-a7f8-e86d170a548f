module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  transformIgnorePatterns: [
    'node_modules/(?!(jose|openid-client|oauth4webapi|preact-render-to-string|@panva)/)',
  ],
  setupFiles: ['<rootDir>/jest.polyfills.js'],
  moduleNameMapper: {
    '^@/components/(.*)$': '<rootDir>/src/components/$1',
    '^@/lib/(.*)$': '<rootDir>/src/lib/$1',
    '^@/app/(.*)$': '<rootDir>/src/app/$1',
    '^@/emails/(.*)$': '<rootDir>/__mocks__/emails/$1',
    '^@/types/(.*)$': '<rootDir>/src/types/$1',
    // Removed Prisma mocking - using real client
  },
  transform: {
    '^.+\\.(ts|tsx)$': ['ts-jest', {
      tsconfig: {
        jsx: 'react-jsx',
      },
    }],
  },
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],
  testMatch: [
    '**/__tests__/**/*.(test|spec).(ts|tsx|js|jsx)',
    '**/*.(test|spec).(ts|tsx|js|jsx)'
  ],
  // Exclude tests that need real database access or have module issues
  testPathIgnorePatterns: [
    '<rootDir>/node_modules/',
    '<rootDir>/__tests__/real-database.test.ts',
    '<rootDir>/__tests__/simple-db-test.test.ts',
    '<rootDir>/__tests__/api-integration.test.ts',
    '<rootDir>/__tests__/api/',
    '<rootDir>/__tests__/e2e/',
    '<rootDir>/__tests__/integration/security.test.ts',
    '<rootDir>/src/app/api/freedom-fund/__tests__/',
  ],
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    'middleware.ts',
    '!src/**/*.d.ts',
    '!**/__tests__/**',
    '!**/node_modules/**',
    '!**/coverage/**',
    '!jest.config.js',
    '!jest.setup.js',
    '!jest.polyfills.js',
  ],
  coverageReporters: ['text', 'lcov', 'html', 'json'],
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 75,
      lines: 80,
      statements: 80
    }
  },
  testTimeout: 30000, // Increased for edge case tests
  maxWorkers: '50%', // Optimize for CI/CD environments
  verbose: true,
};